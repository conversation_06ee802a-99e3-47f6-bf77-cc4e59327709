package com.tvplayer.webdav.ui.player.subtitle

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.security.MessageDigest
import java.util.concurrent.TimeUnit

/**
 * 字幕下载工具类
 * 支持从网络下载字幕文件并缓存到本地
 */
class SubtitleDownloader(private val context: Context) {
    
    companion object {
        private const val TAG = "SubtitleDownloader"
        private const val SUBTITLE_CACHE_DIR = "subtitles"
        private const val CONNECT_TIMEOUT = 30L
        private const val READ_TIMEOUT = 60L
        private const val WRITE_TIMEOUT = 30L
    }
    
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
        .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
        .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
        .build()
    
    /**
     * 下载字幕文件结果
     */
    sealed class DownloadResult {
        data class Success(val filePath: String) : DownloadResult()
        data class Error(val message: String, val exception: Throwable? = null) : DownloadResult()
    }
    
    /**
     * 从指定URL下载字幕文件
     * @param url 字幕文件URL
     * @param forceDownload 是否强制重新下载（忽略缓存）
     * @return 下载结果
     */
    suspend fun downloadSubtitle(url: String, forceDownload: Boolean = false): DownloadResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始下载字幕文件: $url")
                
                // 生成缓存文件名
                val fileName = generateCacheFileName(url)
                val cacheFile = getCacheFile(fileName)
                
                // 检查缓存文件是否存在且有效
                if (!forceDownload && cacheFile.exists() && cacheFile.length() > 0) {
                    Log.d(TAG, "使用缓存的字幕文件: ${cacheFile.absolutePath}")
                    return@withContext DownloadResult.Success(cacheFile.absolutePath)
                }
                
                // 创建缓存目录
                val cacheDir = getCacheDir()
                if (!cacheDir.exists()) {
                    cacheDir.mkdirs()
                }
                
                // 构建请求
                val request = Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "AndroidTVPlayer/1.0")
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate")
                    .build()
                
                // 执行下载
                val response: Response = okHttpClient.newCall(request).execute()
                
                if (!response.isSuccessful) {
                    val errorMsg = "下载失败，HTTP状态码: ${response.code}"
                    Log.e(TAG, errorMsg)
                    return@withContext DownloadResult.Error(errorMsg)
                }
                
                val responseBody = response.body
                if (responseBody == null) {
                    val errorMsg = "响应体为空"
                    Log.e(TAG, errorMsg)
                    return@withContext DownloadResult.Error(errorMsg)
                }
                
                // 写入文件
                FileOutputStream(cacheFile).use { outputStream ->
                    responseBody.byteStream().use { inputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                
                Log.d(TAG, "字幕文件下载成功: ${cacheFile.absolutePath}, 大小: ${cacheFile.length()} bytes")
                
                // 验证文件内容
                if (cacheFile.length() == 0L) {
                    val errorMsg = "下载的文件为空"
                    Log.e(TAG, errorMsg)
                    cacheFile.delete()
                    return@withContext DownloadResult.Error(errorMsg)
                }
                
                DownloadResult.Success(cacheFile.absolutePath)
                
            } catch (e: IOException) {
                val errorMsg = "网络错误: ${e.message}"
                Log.e(TAG, errorMsg, e)
                DownloadResult.Error(errorMsg, e)
            } catch (e: Exception) {
                val errorMsg = "下载字幕文件时发生未知错误: ${e.message}"
                Log.e(TAG, errorMsg, e)
                DownloadResult.Error(errorMsg, e)
            }
        }
    }
    
    /**
     * 生成缓存文件名
     * 使用URL的MD5哈希值作为文件名，避免特殊字符问题
     */
    private fun generateCacheFileName(url: String): String {
        val md5 = MessageDigest.getInstance("MD5")
        val hashBytes = md5.digest(url.toByteArray())
        val hashString = hashBytes.joinToString("") { "%02x".format(it) }
        
        // 根据URL推断文件扩展名
        val extension = when {
            url.contains(".ass", ignoreCase = true) -> ".ass"
            url.contains(".srt", ignoreCase = true) -> ".srt"
            url.contains(".vtt", ignoreCase = true) -> ".vtt"
            else -> ".ass" // 默认使用.ass扩展名
        }
        
        return "$hashString$extension"
    }
    
    /**
     * 获取缓存目录
     */
    private fun getCacheDir(): File {
        return File(context.cacheDir, SUBTITLE_CACHE_DIR)
    }
    
    /**
     * 获取缓存文件
     */
    private fun getCacheFile(fileName: String): File {
        return File(getCacheDir(), fileName)
    }
    
    /**
     * 清理缓存
     * @param maxAgeMillis 最大缓存时间（毫秒），超过此时间的文件将被删除
     */
    suspend fun clearCache(maxAgeMillis: Long = 7 * 24 * 60 * 60 * 1000L) { // 默认7天
        withContext(Dispatchers.IO) {
            try {
                val cacheDir = getCacheDir()
                if (!cacheDir.exists()) return@withContext
                
                val currentTime = System.currentTimeMillis()
                val files = cacheDir.listFiles() ?: return@withContext
                
                var deletedCount = 0
                for (file in files) {
                    if (file.isFile && (currentTime - file.lastModified()) > maxAgeMillis) {
                        if (file.delete()) {
                            deletedCount++
                        }
                    }
                }
                
                Log.d(TAG, "清理缓存完成，删除了 $deletedCount 个文件")
            } catch (e: Exception) {
                Log.e(TAG, "清理缓存时发生错误: ${e.message}", e)
            }
        }
    }
    
    /**
     * 获取缓存大小
     */
    suspend fun getCacheSize(): Long {
        return withContext(Dispatchers.IO) {
            try {
                val cacheDir = getCacheDir()
                if (!cacheDir.exists()) return@withContext 0L
                
                val files = cacheDir.listFiles() ?: return@withContext 0L
                files.filter { it.isFile }.sumOf { it.length() }
            } catch (e: Exception) {
                Log.e(TAG, "获取缓存大小时发生错误: ${e.message}", e)
                0L
            }
        }
    }
    
    /**
     * 检查字幕文件是否已缓存
     */
    fun isSubtitleCached(url: String): Boolean {
        return try {
            val fileName = generateCacheFileName(url)
            val cacheFile = getCacheFile(fileName)
            cacheFile.exists() && cacheFile.length() > 0
        } catch (e: Exception) {
            Log.e(TAG, "检查缓存时发生错误: ${e.message}", e)
            false
        }
    }
    
    /**
     * 获取缓存的字幕文件路径
     */
    fun getCachedSubtitlePath(url: String): String? {
        return try {
            val fileName = generateCacheFileName(url)
            val cacheFile = getCacheFile(fileName)
            if (cacheFile.exists() && cacheFile.length() > 0) {
                cacheFile.absolutePath
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取缓存路径时发生错误: ${e.message}", e)
            null
        }
    }
}
