package com.tvplayer.webdav.ui.player.subtitle

import android.content.Context
import android.media.AudioManager
import android.net.TrafficStats
import android.net.Uri
import android.os.Message
import android.util.Log
import android.view.Surface
import androidx.annotation.Nullable
import androidx.media3.common.Player
import androidx.media3.exoplayer.SeekParameters
import androidx.media3.exoplayer.video.PlaceholderSurface
import com.shuyu.gsyvideoplayer.cache.ICacheManager
import com.shuyu.gsyvideoplayer.model.VideoOptionModel
import com.shuyu.gsyvideoplayer.player.BasePlayerManager
import tv.danmaku.ijk.media.player.IMediaPlayer

/**
 * 支持字幕的播放器管理器
 * 基于 GSYVideoPlayer 官方示例实现
 */
class SubtitlePlayerManager : BasePlayerManager() {

    companion object {
        private const val TAG = "SubtitlePlayerManager"
    }

    private var context: Context? = null
    private var mediaPlayer: SubtitleExoPlayer? = null
    private var surface: Surface? = null
    private var dummySurface: PlaceholderSurface? = null
    private var lastTotalRxBytes = 0L
    private var lastTimeStamp = 0L

    override fun getMediaPlayer(): IMediaPlayer? {
        return mediaPlayer
    }

    override fun initVideoPlayer(
        context: Context,
        msg: Message,
        optionModelList: List<VideoOptionModel>?,
        cacheManager: ICacheManager?
    ) {
        this.context = context.applicationContext
        mediaPlayer = SubtitleExoPlayer(context)
        mediaPlayer?.setAudioStreamType(AudioManager.STREAM_MUSIC)
        
        if (dummySurface == null) {
            dummySurface = PlaceholderSurface.newInstanceV17(context, false)
        }

        // 使用自己的cache模式
        val subtitleModel = msg.obj as? SubtitleModel
        if (subtitleModel != null) {
            try {
                mediaPlayer?.setLooping(subtitleModel.isLooping)
                
                // 设置字幕文件
                if (!subtitleModel.subtitlePath.isNullOrEmpty()) {
                    mediaPlayer?.setSubtitlePath(subtitleModel.subtitlePath)
                    Log.d(TAG, "设置字幕文件: ${subtitleModel.subtitlePath}")
                }
                
                mediaPlayer?.setPreview(subtitleModel.mapHeadData?.isNotEmpty() == true)
                
                if (subtitleModel.isCache && cacheManager != null) {
                    // 通过管理器处理
                    cacheManager.doCacheLogic(
                        context,
                        mediaPlayer,
                        subtitleModel.url,
                        subtitleModel.mapHeadData,
                        subtitleModel.cachePath
                    )
                } else {
                    // 通过自己的内部缓存机制
                    mediaPlayer?.setCache(subtitleModel.isCache)
                    mediaPlayer?.setCacheDir(subtitleModel.cachePath)
                    mediaPlayer?.setOverrideExtension(subtitleModel.overrideExtension)
                    mediaPlayer?.setDataSource(
                        context,
                        Uri.parse(subtitleModel.url),
                        subtitleModel.mapHeadData
                    )
                }
                
                if (subtitleModel.speed != 1f && subtitleModel.speed > 0) {
                    mediaPlayer?.setSpeed(subtitleModel.speed, 1f)
                }
                
                if (subtitleModel.textOutput != null) {
                    mediaPlayer?.setTextOutput(subtitleModel.textOutput)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "初始化播放器时发生错误", e)
            }
        }
        
        initSuccess(subtitleModel)
    }

    override fun showDisplay(msg: Message) {
        if (mediaPlayer == null) {
            return
        }
        
        if (msg.obj == null) {
            mediaPlayer?.setSurface(dummySurface)
        } else {
            val holder = msg.obj as Surface
            surface = holder
            mediaPlayer?.setSurface(holder)
        }
    }

    override fun setSpeed(speed: Float, soundTouch: Boolean) {
        if (mediaPlayer != null) {
            try {
                mediaPlayer?.setSpeed(speed, 1f)
            } catch (e: Exception) {
                Log.e(TAG, "设置播放速度时发生错误", e)
            }
        }
    }

    override fun setNeedMute(needMute: Boolean) {
        if (mediaPlayer != null) {
            if (needMute) {
                mediaPlayer?.setVolume(0f, 0f)
            } else {
                mediaPlayer?.setVolume(1f, 1f)
            }
        }
    }

    override fun setVolume(left: Float, right: Float) {
        if (mediaPlayer != null) {
            mediaPlayer?.setVolume(left, right)
        }
    }

    override fun releaseSurface() {
        if (surface != null) {
            surface = null
        }
    }

    override fun release() {
        if (mediaPlayer != null) {
            mediaPlayer?.setSurface(null)
            mediaPlayer?.release()
            mediaPlayer = null
        }
        if (dummySurface != null) {
            dummySurface?.release()
            dummySurface = null
        }
        lastTotalRxBytes = 0
        lastTimeStamp = 0
    }

    override fun getBufferedPercentage(): Int {
        return mediaPlayer?.getBufferedPercentage() ?: 0
    }

    override fun getNetSpeed(): Long {
        return if (mediaPlayer != null && context != null) {
            getNetSpeed(context!!)
        } else {
            0
        }
    }

    override fun setSpeedPlaying(speed: Float, soundTouch: Boolean) {
        // 实现播放中的速度调整
        setSpeed(speed, soundTouch)
    }

    override fun start() {
        mediaPlayer?.start()
    }

    override fun stop() {
        mediaPlayer?.stop()
    }

    override fun pause() {
        mediaPlayer?.pause()
    }

    override fun getVideoWidth(): Int {
        return mediaPlayer?.getVideoWidth() ?: 0
    }

    override fun getVideoHeight(): Int {
        return mediaPlayer?.getVideoHeight() ?: 0
    }

    override fun isPlaying(): Boolean {
        return mediaPlayer?.isPlaying() ?: false
    }

    override fun seekTo(time: Long) {
        mediaPlayer?.seekTo(time)
    }

    override fun getCurrentPosition(): Long {
        return mediaPlayer?.getCurrentPosition() ?: 0
    }

    override fun getDuration(): Long {
        return mediaPlayer?.getDuration() ?: 0
    }

    override fun getVideoSarNum(): Int {
        return mediaPlayer?.getVideoSarNum() ?: 1
    }

    override fun getVideoSarDen(): Int {
        return mediaPlayer?.getVideoSarDen() ?: 1
    }

    override fun isSurfaceSupportLockCanvas(): Boolean {
        return false
    }

    /**
     * 添加字幕输出监听器
     */
    fun addTextOutputPlaying(textOutput: Player.Listener) {
        mediaPlayer?.addTextOutputPlaying(textOutput)
    }

    /**
     * 移除字幕输出监听器
     */
    fun removeTextOutput(textOutput: Player.Listener) {
        mediaPlayer?.removeTextOutput(textOutput)
    }

    /**
     * 设置seek的临近帧
     */
    fun setSeekParameter(@Nullable seekParameters: SeekParameters?) {
        mediaPlayer?.setSeekParameter(seekParameters)
    }

    /**
     * 获取网络速度
     */
    private fun getNetSpeed(context: Context): Long {
        val nowTotalRxBytes = if (TrafficStats.getUidRxBytes(context.applicationInfo.uid) == TrafficStats.UNSUPPORTED) {
            0
        } else {
            TrafficStats.getTotalRxBytes() / 1024 // 转为KB
        }
        
        val nowTimeStamp = System.currentTimeMillis()
        val calculationTime = nowTimeStamp - lastTimeStamp
        
        if (calculationTime == 0L) {
            return 0
        }
        
        // 毫秒转换
        val speed = (nowTotalRxBytes - lastTotalRxBytes) * 1000 / calculationTime
        lastTimeStamp = nowTimeStamp
        lastTotalRxBytes = nowTotalRxBytes
        return speed
    }
}
