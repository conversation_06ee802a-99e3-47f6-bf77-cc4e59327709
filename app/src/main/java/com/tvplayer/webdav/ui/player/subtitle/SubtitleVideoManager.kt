package com.tvplayer.webdav.ui.player.subtitle

import android.content.Context
import android.util.Log
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.player.IPlayerManager
import com.shuyu.gsyvideoplayer.player.PlayerFactory
import com.shuyu.gsyvideoplayer.video.base.GSYVideoPlayer

/**
 * 支持字幕的视频管理器
 * 基于GSYVideoPlayer官方示例实现
 */
class SubtitleVideoManager private constructor() {

    companion object {
        private const val TAG = "SubtitleVideoManager"
        const val FULLSCREEN_ID = 199200
        const val SMALL_ID = 199201

        @Volatile
        private var INSTANCE: SubtitleVideoManager? = null

        fun instance(): SubtitleVideoManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SubtitleVideoManager().also { INSTANCE = it }
            }
        }

        /**
         * 退出全屏
         */
        fun backFromWindowFull(context: Context): Boolean {
            return GSYVideoManager.backFromWindowFull(context)
        }

        /**
         * 释放所有视频
         */
        fun releaseAllVideos() {
            GSYVideoManager.releaseAllVideos()
        }
    }

    val gsyVideoManager = GSYVideoManager.instance()

    fun getPlayManager(): IPlayerManager {
        return SubtitlePlayerManager()
    }

    // 委托方法
    fun listener() = gsyVideoManager.listener()
    fun setListener(listener: com.shuyu.gsyvideoplayer.listener.VideoAllCallBack?) {
        // 使用反射调用 setListener 方法
        try {
            val method = GSYVideoManager::class.java.getDeclaredMethod("setListener", com.shuyu.gsyvideoplayer.listener.GSYMediaPlayerListener::class.java)
            method.isAccessible = true
            method.invoke(gsyVideoManager, listener)
        } catch (e: Exception) {
            Log.e(TAG, "设置监听器失败", e)
        }
    }
    fun setPlayTag(playTag: String?) = gsyVideoManager.setPlayTag(playTag)
    fun setPlayPosition(playPosition: Int) = gsyVideoManager.setPlayPosition(playPosition)
    fun sendMessage(message: android.os.Message) {
        // 使用反射调用 sendMessage 方法
        try {
            val method = GSYVideoManager::class.java.getDeclaredMethod("sendMessage", android.os.Message::class.java)
            method.isAccessible = true
            method.invoke(gsyVideoManager, message)
        } catch (e: Exception) {
            Log.e(TAG, "发送消息失败", e)
        }
    }
    fun getPlayer() = gsyVideoManager.player
    fun initContext(context: Context) = gsyVideoManager.initContext(context)

    fun prepare(
        url: String,
        mapHeadData: Map<String, String>,
        loop: Boolean,
        speed: Float,
        cache: Boolean,
        cachePath: String?,
        overrideExtension: String?
    ) {
        prepare(url, null, mapHeadData, loop, speed, cache, cachePath, overrideExtension)
    }

    /**
     * 带字幕的准备方法
     */
    fun prepare(
        url: String,
        subtitlePath: String?,
        mapHeadData: Map<String, String>,
        loop: Boolean,
        speed: Float,
        cache: Boolean,
        cachePath: String?,
        overrideExtension: String?
    ) {
        if (listener() == null) return

        Log.d(TAG, "准备播放: url=$url, subtitle=$subtitlePath")

        val subtitleModel = SubtitleModel(
            url = url,
            subtitlePath = subtitlePath,
            isLooping = loop,
            mapHeadData = mapHeadData,
            isCache = cache,
            cachePath = cachePath,
            overrideExtension = overrideExtension,
            speed = speed
        )

        val message = android.os.Message()
        message.what = 0 // PREPARE_URL 常量值
        message.obj = subtitleModel
        sendMessage(message)
    }

    /**
     * 带字幕输出监听器的准备方法
     */
    fun prepare(
        url: String,
        subtitlePath: String?,
        gsyVideoPlayer: GSYVideoPlayer,
        mapHeadData: Map<String, String>,
        loop: Boolean,
        speed: Float,
        cache: Boolean,
        cachePath: String?,
        overrideExtension: String?
    ) {
        if (listener() == null) return

        Log.d(TAG, "准备播放（带监听器）: url=$url, subtitle=$subtitlePath")

        val subtitleModel = SubtitleModel(
            url = url,
            subtitlePath = subtitlePath,
            isLooping = loop,
            mapHeadData = mapHeadData,
            isCache = cache,
            cachePath = cachePath,
            overrideExtension = overrideExtension,
            speed = speed,
            textOutput = gsyVideoPlayer as? androidx.media3.common.Player.Listener
        )

        val message = android.os.Message()
        message.what = 0 // PREPARE_URL 常量值
        message.obj = subtitleModel
        sendMessage(message)
    }

    /**
     * 获取字幕播放器管理器
     */
    fun getSubtitlePlayerManager(): SubtitlePlayerManager? {
        return getPlayer() as? SubtitlePlayerManager
    }

    /**
     * 添加字幕输出监听器
     */
    fun addTextOutputPlaying(textOutput: androidx.media3.common.Player.Listener) {
        getSubtitlePlayerManager()?.addTextOutputPlaying(textOutput)
    }

    /**
     * 移除字幕输出监听器
     */
    fun removeTextOutput(textOutput: androidx.media3.common.Player.Listener) {
        getSubtitlePlayerManager()?.removeTextOutput(textOutput)
    }

    /**
     * 设置播放器工厂
     */
    fun setPlayerFactory() {
        PlayerFactory.setPlayManager(SubtitlePlayerManager::class.java)
        Log.d(TAG, "设置字幕播放器工厂")
    }
}
