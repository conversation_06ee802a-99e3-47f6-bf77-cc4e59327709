package com.tvplayer.webdav.ui.player.subtitle

import android.content.Context
import android.util.Log
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.player.IPlayerManager
import com.shuyu.gsyvideoplayer.player.PlayerFactory
import com.shuyu.gsyvideoplayer.video.base.GSYVideoPlayer

/**
 * 支持字幕的视频管理器
 * 基于GSYVideoPlayer官方示例实现
 */
class SubtitleVideoManager private constructor() : GSYVideoManager() {

    companion object {
        private const val TAG = "SubtitleVideoManager"
        const val FULLSCREEN_ID = 199200
        const val SMALL_ID = 199201

        @Volatile
        private var INSTANCE: SubtitleVideoManager? = null

        fun instance(): SubtitleVideoManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SubtitleVideoManager().also { INSTANCE = it }
            }
        }

        /**
         * 退出全屏
         */
        fun backFromWindowFull(context: Context): Boolean {
            var backFromFull = false
            if (instance().lastListener() != null) {
                instance().lastListener().onBackFullscreen()
                backFromFull = true
            }
            instance().releaseMediaPlayer()
            return backFromFull
        }

        /**
         * 释放所有视频
         */
        fun releaseAllVideos() {
            instance().releaseMediaPlayer()
        }
    }

    override fun getPlayManager(): IPlayerManager {
        return SubtitlePlayerManager()
    }

    override fun prepare(
        url: String,
        mapHeadData: Map<String, String>,
        loop: Boolean,
        speed: Float,
        cache: Boolean,
        cachePath: String?,
        overrideExtension: String?
    ) {
        prepare(url, null, mapHeadData, loop, speed, cache, cachePath, overrideExtension)
    }

    /**
     * 带字幕的准备方法
     */
    fun prepare(
        url: String,
        subtitlePath: String?,
        mapHeadData: Map<String, String>,
        loop: Boolean,
        speed: Float,
        cache: Boolean,
        cachePath: String?,
        overrideExtension: String?
    ) {
        if (listener() == null) return

        Log.d(TAG, "准备播放: url=$url, subtitle=$subtitlePath")

        val subtitleModel = SubtitleModel(
            url = url,
            subtitlePath = subtitlePath,
            isLooping = loop,
            mapHeadData = mapHeadData,
            isCache = cache,
            cachePath = cachePath,
            overrideExtension = overrideExtension,
            speed = speed
        )

        val message = android.os.Message()
        message.what = PREPARE_URL
        message.obj = subtitleModel
        sendMessage(message)
    }

    /**
     * 带字幕输出监听器的准备方法
     */
    fun prepare(
        url: String,
        subtitlePath: String?,
        gsyVideoPlayer: GSYVideoPlayer,
        mapHeadData: Map<String, String>,
        loop: Boolean,
        speed: Float,
        cache: Boolean,
        cachePath: String?,
        overrideExtension: String?
    ) {
        if (listener() == null) return

        Log.d(TAG, "准备播放（带监听器）: url=$url, subtitle=$subtitlePath")

        val subtitleModel = SubtitleModel(
            url = url,
            subtitlePath = subtitlePath,
            isLooping = loop,
            mapHeadData = mapHeadData,
            isCache = cache,
            cachePath = cachePath,
            overrideExtension = overrideExtension,
            speed = speed,
            textOutput = gsyVideoPlayer as? androidx.media3.common.Player.Listener
        )

        val message = android.os.Message()
        message.what = PREPARE_URL
        message.obj = subtitleModel
        sendMessage(message)
    }

    /**
     * 获取字幕播放器管理器
     */
    fun getSubtitlePlayerManager(): SubtitlePlayerManager? {
        return getPlayer() as? SubtitlePlayerManager
    }

    /**
     * 添加字幕输出监听器
     */
    fun addTextOutputPlaying(textOutput: androidx.media3.common.Player.Listener) {
        getSubtitlePlayerManager()?.addTextOutputPlaying(textOutput)
    }

    /**
     * 移除字幕输出监听器
     */
    fun removeTextOutput(textOutput: androidx.media3.common.Player.Listener) {
        getSubtitlePlayerManager()?.removeTextOutput(textOutput)
    }

    /**
     * 设置播放器工厂
     */
    fun setPlayerFactory() {
        PlayerFactory.setPlayManager(SubtitlePlayerManager::class.java)
        Log.d(TAG, "设置字幕播放器工厂")
    }
}
