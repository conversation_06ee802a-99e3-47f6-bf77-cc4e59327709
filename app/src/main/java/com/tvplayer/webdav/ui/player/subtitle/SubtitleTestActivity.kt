package com.tvplayer.webdav.ui.player.subtitle

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.tvplayer.webdav.R
import com.tvplayer.webdav.ui.player.PlayerActivity
import kotlinx.coroutines.launch

/**
 * 字幕功能测试Activity
 * 用于测试字幕下载、解析和显示功能
 */
class SubtitleTestActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "SubtitleTestActivity"
        
        // 测试视频URL（可以使用任何公开的视频URL）
        private const val TEST_VIDEO_URL = "http://vjs.zencdn.net/v/oceans.mp4"
        
        // 测试字幕URL
        private const val TEST_SUBTITLE_URL = "http://file1.assrt.net/onthefly/665530/-/1/Shaolin.Soccer.2001.CHINESE.1080p.BluRay.x264.DTS-HD.MA.5.1-FGT.ass?_=1755915412&-=a05bb65a0d898d3beb029cd6cab16212&api=1"
    }

    private lateinit var subtitleDownloader: SubtitleDownloader

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_subtitle_test)

        subtitleDownloader = SubtitleDownloader(this)
        
        setupButtons()
    }

    private fun setupButtons() {
        // 测试字幕下载按钮
        findViewById<Button>(R.id.btn_test_download).setOnClickListener {
            testSubtitleDownload()
        }

        // 测试播放带字幕视频按钮
        findViewById<Button>(R.id.btn_test_playback).setOnClickListener {
            testVideoPlaybackWithSubtitle()
        }

        // 测试播放无字幕视频按钮
        findViewById<Button>(R.id.btn_test_playback_no_subtitle).setOnClickListener {
            testVideoPlaybackWithoutSubtitle()
        }

        // 清理缓存按钮
        findViewById<Button>(R.id.btn_clear_cache).setOnClickListener {
            clearSubtitleCache()
        }

        // 检查缓存状态按钮
        findViewById<Button>(R.id.btn_check_cache).setOnClickListener {
            checkCacheStatus()
        }
    }

    /**
     * 测试字幕下载功能
     */
    private fun testSubtitleDownload() {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "开始测试字幕下载...")
                Toast.makeText(this@SubtitleTestActivity, "开始下载字幕...", Toast.LENGTH_SHORT).show()

                when (val result = subtitleDownloader.downloadSubtitle(TEST_SUBTITLE_URL)) {
                    is SubtitleDownloader.DownloadResult.Success -> {
                        val message = "字幕下载成功！\n路径: ${result.filePath}"
                        Log.d(TAG, message)
                        Toast.makeText(this@SubtitleTestActivity, message, Toast.LENGTH_LONG).show()
                    }
                    is SubtitleDownloader.DownloadResult.Error -> {
                        val message = "字幕下载失败: ${result.message}"
                        Log.e(TAG, message)
                        Toast.makeText(this@SubtitleTestActivity, message, Toast.LENGTH_LONG).show()
                    }
                }
            } catch (e: Exception) {
                val message = "下载过程中发生异常: ${e.message}"
                Log.e(TAG, message, e)
                Toast.makeText(this@SubtitleTestActivity, message, Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * 测试带字幕的视频播放
     */
    private fun testVideoPlaybackWithSubtitle() {
        try {
            Log.d(TAG, "启动带字幕的视频播放测试...")
            
            val intent = PlayerActivity.intentForWithSubtitle(
                this,
                "测试视频（带字幕）",
                Uri.parse(TEST_VIDEO_URL),
                TEST_SUBTITLE_URL
            )
            
            startActivity(intent)
            
        } catch (e: Exception) {
            val message = "启动播放器失败: ${e.message}"
            Log.e(TAG, message, e)
            Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 测试无字幕的视频播放
     */
    private fun testVideoPlaybackWithoutSubtitle() {
        try {
            Log.d(TAG, "启动无字幕的视频播放测试...")
            
            val intent = PlayerActivity.intentFor(
                this,
                "测试视频（无字幕）",
                Uri.parse(TEST_VIDEO_URL)
            )
            
            startActivity(intent)
            
        } catch (e: Exception) {
            val message = "启动播放器失败: ${e.message}"
            Log.e(TAG, message, e)
            Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 清理字幕缓存
     */
    private fun clearSubtitleCache() {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "开始清理字幕缓存...")
                Toast.makeText(this@SubtitleTestActivity, "正在清理缓存...", Toast.LENGTH_SHORT).show()

                subtitleDownloader.clearCache()
                
                val message = "缓存清理完成"
                Log.d(TAG, message)
                Toast.makeText(this@SubtitleTestActivity, message, Toast.LENGTH_SHORT).show()
                
            } catch (e: Exception) {
                val message = "清理缓存失败: ${e.message}"
                Log.e(TAG, message, e)
                Toast.makeText(this@SubtitleTestActivity, message, Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * 检查缓存状态
     */
    private fun checkCacheStatus() {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "检查缓存状态...")
                
                val isCached = subtitleDownloader.isSubtitleCached(TEST_SUBTITLE_URL)
                val cacheSize = subtitleDownloader.getCacheSize()
                val cachePath = subtitleDownloader.getCachedSubtitlePath(TEST_SUBTITLE_URL)
                
                val message = buildString {
                    appendLine("缓存状态:")
                    appendLine("是否已缓存: $isCached")
                    appendLine("缓存大小: ${cacheSize / 1024}KB")
                    if (cachePath != null) {
                        appendLine("缓存路径: $cachePath")
                    }
                }
                
                Log.d(TAG, message)
                Toast.makeText(this@SubtitleTestActivity, message, Toast.LENGTH_LONG).show()
                
            } catch (e: Exception) {
                val message = "检查缓存状态失败: ${e.message}"
                Log.e(TAG, message, e)
                Toast.makeText(this@SubtitleTestActivity, message, Toast.LENGTH_LONG).show()
            }
        }
    }
}
