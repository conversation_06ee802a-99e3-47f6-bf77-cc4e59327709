package com.tvplayer.webdav.ui.player.subtitle

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Color
import androidx.media3.ui.CaptionStyleCompat

/**
 * 字幕样式配置管理器
 * 用于保存和加载用户的字幕样式偏好设置
 */
class SubtitleStyleConfig(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "subtitle_style_prefs"
        private const val KEY_TEXT_COLOR = "text_color"
        private const val KEY_BACKGROUND_COLOR = "background_color"
        private const val KEY_WINDOW_COLOR = "window_color"
        private const val KEY_EDGE_TYPE = "edge_type"
        private const val KEY_EDGE_COLOR = "edge_color"
        private const val KEY_TEXT_SIZE = "text_size"
        private const val KEY_SUBTITLE_ENABLED = "subtitle_enabled"
        
        // 默认值
        private const val DEFAULT_TEXT_COLOR = Color.WHITE
        private const val DEFAULT_BACKGROUND_COLOR = Color.TRANSPARENT
        private const val DEFAULT_WINDOW_COLOR = Color.TRANSPARENT
        private const val DEFAULT_EDGE_TYPE = CaptionStyleCompat.EDGE_TYPE_OUTLINE
        private const val DEFAULT_EDGE_COLOR = Color.BLACK
        private const val DEFAULT_TEXT_SIZE = 16f
        private const val DEFAULT_SUBTITLE_ENABLED = true
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * 字幕样式数据类
     */
    data class SubtitleStyle(
        val textColor: Int = DEFAULT_TEXT_COLOR,
        val backgroundColor: Int = DEFAULT_BACKGROUND_COLOR,
        val windowColor: Int = DEFAULT_WINDOW_COLOR,
        val edgeType: Int = DEFAULT_EDGE_TYPE,
        val edgeColor: Int = DEFAULT_EDGE_COLOR,
        val textSize: Float = DEFAULT_TEXT_SIZE,
        val isEnabled: Boolean = DEFAULT_SUBTITLE_ENABLED
    ) {
        /**
         * 转换为ExoPlayer的CaptionStyleCompat
         */
        fun toCaptionStyle(): CaptionStyleCompat {
            return CaptionStyleCompat(
                textColor,
                backgroundColor,
                windowColor,
                edgeType,
                edgeColor,
                null // 字体，使用默认
            )
        }
    }
    
    /**
     * 保存字幕样式
     */
    fun saveStyle(style: SubtitleStyle) {
        prefs.edit().apply {
            putInt(KEY_TEXT_COLOR, style.textColor)
            putInt(KEY_BACKGROUND_COLOR, style.backgroundColor)
            putInt(KEY_WINDOW_COLOR, style.windowColor)
            putInt(KEY_EDGE_TYPE, style.edgeType)
            putInt(KEY_EDGE_COLOR, style.edgeColor)
            putFloat(KEY_TEXT_SIZE, style.textSize)
            putBoolean(KEY_SUBTITLE_ENABLED, style.isEnabled)
            apply()
        }
    }
    
    /**
     * 加载字幕样式
     */
    fun loadStyle(): SubtitleStyle {
        return SubtitleStyle(
            textColor = prefs.getInt(KEY_TEXT_COLOR, DEFAULT_TEXT_COLOR),
            backgroundColor = prefs.getInt(KEY_BACKGROUND_COLOR, DEFAULT_BACKGROUND_COLOR),
            windowColor = prefs.getInt(KEY_WINDOW_COLOR, DEFAULT_WINDOW_COLOR),
            edgeType = prefs.getInt(KEY_EDGE_TYPE, DEFAULT_EDGE_TYPE),
            edgeColor = prefs.getInt(KEY_EDGE_COLOR, DEFAULT_EDGE_COLOR),
            textSize = prefs.getFloat(KEY_TEXT_SIZE, DEFAULT_TEXT_SIZE),
            isEnabled = prefs.getBoolean(KEY_SUBTITLE_ENABLED, DEFAULT_SUBTITLE_ENABLED)
        )
    }
    
    /**
     * 重置为默认样式
     */
    fun resetToDefault() {
        saveStyle(SubtitleStyle())
    }
    
    /**
     * 获取预设样式列表
     */
    fun getPresetStyles(): List<Pair<String, SubtitleStyle>> {
        return listOf(
            "默认" to SubtitleStyle(),
            "黄色字幕" to SubtitleStyle(
                textColor = Color.YELLOW,
                edgeType = CaptionStyleCompat.EDGE_TYPE_OUTLINE,
                edgeColor = Color.BLACK
            ),
            "绿色字幕" to SubtitleStyle(
                textColor = Color.GREEN,
                edgeType = CaptionStyleCompat.EDGE_TYPE_OUTLINE,
                edgeColor = Color.BLACK
            ),
            "蓝色字幕" to SubtitleStyle(
                textColor = Color.CYAN,
                edgeType = CaptionStyleCompat.EDGE_TYPE_OUTLINE,
                edgeColor = Color.BLACK
            ),
            "红色字幕" to SubtitleStyle(
                textColor = Color.RED,
                edgeType = CaptionStyleCompat.EDGE_TYPE_OUTLINE,
                edgeColor = Color.BLACK
            ),
            "黑底白字" to SubtitleStyle(
                textColor = Color.WHITE,
                backgroundColor = Color.BLACK,
                edgeType = CaptionStyleCompat.EDGE_TYPE_NONE
            ),
            "半透明背景" to SubtitleStyle(
                textColor = Color.WHITE,
                backgroundColor = Color.argb(128, 0, 0, 0),
                edgeType = CaptionStyleCompat.EDGE_TYPE_NONE
            ),
            "大字体" to SubtitleStyle(
                textSize = 20f,
                edgeType = CaptionStyleCompat.EDGE_TYPE_OUTLINE,
                edgeColor = Color.BLACK
            ),
            "小字体" to SubtitleStyle(
                textSize = 12f,
                edgeType = CaptionStyleCompat.EDGE_TYPE_OUTLINE,
                edgeColor = Color.BLACK
            )
        )
    }
    
    /**
     * 检查字幕是否启用
     */
    fun isSubtitleEnabled(): Boolean {
        return prefs.getBoolean(KEY_SUBTITLE_ENABLED, DEFAULT_SUBTITLE_ENABLED)
    }
    
    /**
     * 设置字幕启用状态
     */
    fun setSubtitleEnabled(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_SUBTITLE_ENABLED, enabled).apply()
    }
    
    /**
     * 获取文字颜色选项
     */
    fun getTextColorOptions(): List<Pair<String, Int>> {
        return listOf(
            "白色" to Color.WHITE,
            "黄色" to Color.YELLOW,
            "绿色" to Color.GREEN,
            "蓝色" to Color.CYAN,
            "红色" to Color.RED,
            "紫色" to Color.MAGENTA,
            "橙色" to Color.rgb(255, 165, 0)
        )
    }
    
    /**
     * 获取边缘类型选项
     */
    fun getEdgeTypeOptions(): List<Pair<String, Int>> {
        return listOf(
            "无边缘" to CaptionStyleCompat.EDGE_TYPE_NONE,
            "轮廓" to CaptionStyleCompat.EDGE_TYPE_OUTLINE,
            "投影" to CaptionStyleCompat.EDGE_TYPE_DROP_SHADOW,
            "凸起" to CaptionStyleCompat.EDGE_TYPE_RAISED,
            "凹陷" to CaptionStyleCompat.EDGE_TYPE_DEPRESSED
        )
    }
    
    /**
     * 获取字体大小选项
     */
    fun getTextSizeOptions(): List<Pair<String, Float>> {
        return listOf(
            "很小" to 10f,
            "小" to 12f,
            "正常" to 16f,
            "大" to 20f,
            "很大" to 24f,
            "超大" to 28f
        )
    }
}
