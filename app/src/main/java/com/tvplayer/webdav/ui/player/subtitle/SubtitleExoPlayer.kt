package com.tvplayer.webdav.ui.player.subtitle

import android.content.Context
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.media3.common.C
import androidx.media3.common.Format
import androidx.media3.common.MediaItem
import androidx.media3.common.MimeTypes
import androidx.media3.common.Player
import androidx.media3.common.TrackSelectionParameters
import androidx.media3.common.text.Cue
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.DefaultRenderersFactory
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.MergingMediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector
import androidx.media3.exoplayer.upstream.DefaultBandwidthMeter
import androidx.media3.extractor.Extractor
import androidx.media3.extractor.ExtractorsFactory
import androidx.media3.extractor.text.DefaultSubtitleParserFactory
import androidx.media3.extractor.text.SubtitleExtractor
import androidx.media3.extractor.text.SubtitleParser
import tv.danmaku.ijk.media.exo2.IjkExo2MediaPlayer
import tv.danmaku.ijk.media.exo2.demo.EventLogger

/**
 * 支持字幕的ExoPlayer播放器
 * 基于GSYVideoPlayer官方示例实现
 */
class SubtitleExoPlayer(context: Context) : IjkExo2MediaPlayer(context) {

    companion object {
        private const val TAG = "SubtitleExoPlayer"
    }

    private var subtitlePath: String? = null
    private var textOutput: Player.Listener? = null

    override fun onCues(cues: List<Cue>) {
        super.onCues(cues)
        // 字幕回调处理
        Log.d(TAG, "收到字幕: ${cues.size} 条")
    }

    override fun prepareAsyncInternal() {
        Handler(Looper.getMainLooper()).post {
            try {
                if (mTrackSelector == null) {
                    // 设置 setSelectUndeterminedTextLanguage 无视语言选择
                    mTrackSelector = DefaultTrackSelector(
                        mAppContext,
                        TrackSelectionParameters.Builder(mAppContext)
                            .setSelectUndeterminedTextLanguage(true)
                            .build()
                    )
                }
                
                mEventLogger = EventLogger(mTrackSelector)
                
                val preferExtensionDecoders = true
                val useExtensionRenderers = true // 是否开启扩展
                
                @DefaultRenderersFactory.ExtensionRendererMode 
                val extensionRendererMode = if (useExtensionRenderers) {
                    if (preferExtensionDecoders) {
                        DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER
                    } else {
                        DefaultRenderersFactory.EXTENSION_RENDERER_MODE_ON
                    }
                } else {
                    DefaultRenderersFactory.EXTENSION_RENDERER_MODE_OFF
                }
                
                if (mRendererFactory == null) {
                    mRendererFactory = DefaultRenderersFactory(mAppContext)
                    mRendererFactory.setExtensionRendererMode(extensionRendererMode)
                }
                
                if (mLoadControl == null) {
                    mLoadControl = DefaultLoadControl()
                }
                
                mInternalPlayer = ExoPlayer.Builder(mAppContext, mRendererFactory)
                    .setLooper(Looper.getMainLooper())
                    .setTrackSelector(mTrackSelector)
                    .setLoadControl(mLoadControl)
                    .build()
                
                mInternalPlayer.addListener(this)
                mInternalPlayer.addAnalyticsListener(this)
                
                if (textOutput != null) {
                    mInternalPlayer.addListener(textOutput)
                }
                
                mInternalPlayer.addListener(mEventLogger)
                
                if (mSpeedPlaybackParameters != null) {
                    mInternalPlayer.setPlaybackParameters(mSpeedPlaybackParameters)
                }
                
                if (mSurface != null) {
                    mInternalPlayer.setVideoSurface(mSurface)
                }

                // 如果有字幕文件，创建合并的媒体源
                if (!subtitlePath.isNullOrEmpty()) {
                    val textMediaSource = getTextSource(Uri.parse(subtitlePath))
                    mMediaSource = MergingMediaSource(mMediaSource, textMediaSource)
                    Log.d(TAG, "添加字幕源: $subtitlePath")
                }
                
                mInternalPlayer.setMediaSource(mMediaSource)
                mInternalPlayer.prepare()
                mInternalPlayer.setPlayWhenReady(false)
                
                Log.d(TAG, "播放器初始化完成")
                
            } catch (e: Exception) {
                Log.e(TAG, "初始化播放器时发生错误", e)
            }
        }
    }

    /**
     * 创建字幕媒体源
     */
    private fun getTextSource(subtitleUri: Uri): MediaSource {
        // 根据文件扩展名确定MIME类型
        val mimeType = when {
            subtitlePath?.endsWith(".ass", ignoreCase = true) == true -> "text/x-ssa"
            subtitlePath?.endsWith(".srt", ignoreCase = true) == true -> MimeTypes.APPLICATION_SUBRIP
            subtitlePath?.endsWith(".vtt", ignoreCase = true) == true -> MimeTypes.TEXT_VTT
            else -> "text/x-ssa" // 默认使用ASS格式
        }
        
        Log.d(TAG, "字幕MIME类型: $mimeType")
        
        val format = Format.Builder()
            .setSampleMimeType(mimeType)
            .setSelectionFlags(C.SELECTION_FLAG_FORCED)
            .setLanguage(null) // 设置为null以避免语言匹配问题
            .build()

        val subtitle = MediaItem.SubtitleConfiguration.Builder(subtitleUri)
            .setMimeType(format.sampleMimeType!!)
            .setLanguage(format.language)
            .setSelectionFlags(format.selectionFlags)
            .build()

        val factory = DefaultHttpDataSource.Factory()
            .setAllowCrossProtocolRedirects(true)
            .setConnectTimeoutMs(50000)
            .setReadTimeoutMs(50000)
            .setTransferListener(DefaultBandwidthMeter.Builder(mAppContext).build())

        val subtitleParserFactory = DefaultSubtitleParserFactory()

        val extractorsFactory = ExtractorsFactory {
            arrayOf(
                SubtitleExtractor(subtitleParserFactory.create(format), format)
            )
        }

        val progressiveMediaSourceFactory = ProgressiveMediaSource.Factory(
            DefaultDataSource.Factory(mAppContext, factory),
            extractorsFactory
        )

        return progressiveMediaSourceFactory.createMediaSource(
            MediaItem.fromUri(subtitle.uri.toString())
        )
    }

    /**
     * 设置字幕文件路径
     */
    fun setSubtitlePath(path: String?) {
        this.subtitlePath = path
        Log.d(TAG, "设置字幕路径: $path")
    }

    /**
     * 获取字幕文件路径
     */
    fun getSubtitlePath(): String? {
        return subtitlePath
    }

    /**
     * 设置字幕输出监听器
     */
    fun setTextOutput(textOutput: Player.Listener?) {
        this.textOutput = textOutput
    }

    /**
     * 获取字幕输出监听器
     */
    fun getTextOutput(): Player.Listener? {
        return textOutput
    }

    /**
     * 添加字幕输出监听器（播放中）
     */
    fun addTextOutputPlaying(textOutput: Player.Listener) {
        if (mInternalPlayer != null) {
            mInternalPlayer.addListener(textOutput)
        }
    }

    /**
     * 移除字幕输出监听器
     */
    fun removeTextOutput(textOutput: Player.Listener) {
        if (mInternalPlayer != null) {
            mInternalPlayer.removeListener(textOutput)
        }
    }
}
