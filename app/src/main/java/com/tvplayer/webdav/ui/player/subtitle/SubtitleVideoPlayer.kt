package com.tvplayer.webdav.ui.player.subtitle

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.util.Log
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.media3.common.Player
import androidx.media3.common.text.CueGroup
import androidx.media3.ui.CaptionStyleCompat
import androidx.media3.ui.SubtitleView
import com.shuyu.gsyvideoplayer.utils.Debuger
import com.shuyu.gsyvideoplayer.video.NormalGSYVideoPlayer
import com.shuyu.gsyvideoplayer.video.base.GSYBaseVideoPlayer
import com.shuyu.gsyvideoplayer.video.base.GSYVideoPlayer
import com.tvplayer.webdav.R

/**
 * 支持字幕的视频播放器组件
 * 基于GSYVideoPlayer官方示例实现
 */
class SubtitleVideoPlayer : NormalGSYVideoPlayer, Player.Listener {

    companion object {
        private const val TAG = "SubtitleVideoPlayer"
    }

    private var subtitleView: SubtitleView? = null
    private var subtitlePath: String? = null
    private lateinit var styleConfig: SubtitleStyleConfig

    constructor(context: Context, fullFlag: Boolean?) : super(context, fullFlag)
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    override fun init(context: Context) {
        super.init(context)
        styleConfig = SubtitleStyleConfig(context)
        initSubtitleView()
    }

    /**
     * 初始化字幕视图
     */
    private fun initSubtitleView() {
        subtitleView = findViewById(R.id.subtitle_view)
        if (subtitleView != null) {
            // 加载用户保存的字幕样式
            applySubtitleStyle()
            Log.d(TAG, "字幕视图初始化完成")
        } else {
            Log.w(TAG, "未找到字幕视图组件")
        }
    }

    /**
     * 应用字幕样式
     */
    private fun applySubtitleStyle() {
        if (subtitleView != null) {
            val style = styleConfig.loadStyle()
            subtitleView?.setStyle(style.toCaptionStyle())
            subtitleView?.setFixedTextSize(TypedValue.COMPLEX_UNIT_SP, style.textSize)
            subtitleView?.visibility = if (style.isEnabled) View.VISIBLE else View.GONE
            Log.d(TAG, "应用字幕样式: 颜色=${style.textColor}, 大小=${style.textSize}, 启用=${style.isEnabled}")
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.layout_subtitle_video_player
    }

    override fun startPrepare() {
        if (getGSYVideoManager().listener() != null) {
            getGSYVideoManager().listener().onCompletion()
        }
        
        if (mVideoAllCallBack != null) {
            Debuger.printfLog("onStartPrepared")
            mVideoAllCallBack.onStartPrepared(mOriginUrl, mTitle, this)
        }
        
        getGSYVideoManager().setListener(this)
        getGSYVideoManager().setPlayTag(mPlayTag)
        getGSYVideoManager().setPlayPosition(mPlayPosition)
        
        // Audio focus is now handled by the base class GSYAudioFocusManager
        try {
            if (mContext is Activity) {
                (mContext as Activity).window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置屏幕常亮失败", e)
        }
        
        mBackUpPlayingBufferState = -1

        // 使用自定义的字幕视频管理器
        getSubtitleVideoManager().prepare(
            mUrl,
            subtitlePath,
            this,
            mMapHeadData ?: HashMap(),
            mLooping,
            mSpeed,
            mCache,
            mCachePath,
            mOverrideExtension
        )
        
        setStateAndUi(CURRENT_STATE_PREPAREING)
        Log.d(TAG, "开始准备播放，字幕路径: $subtitlePath")
    }

    override fun onCues(cueGroup: CueGroup) {
        if (subtitleView != null) {
            subtitleView?.setCues(cueGroup.cues)
            Log.d(TAG, "显示字幕: ${cueGroup.cues.size} 条")
        }
    }

    /**
     * 设置字幕文件路径
     */
    fun setSubtitlePath(path: String?) {
        this.subtitlePath = path
        Log.d(TAG, "设置字幕路径: $path")
    }

    /**
     * 获取字幕文件路径
     */
    fun getSubtitlePath(): String? {
        return subtitlePath
    }

    /**
     * 设置字幕样式
     */
    fun setSubtitleStyle(style: SubtitleStyleConfig.SubtitleStyle) {
        if (subtitleView != null) {
            subtitleView?.setStyle(style.toCaptionStyle())
            subtitleView?.setFixedTextSize(TypedValue.COMPLEX_UNIT_SP, style.textSize)
            subtitleView?.visibility = if (style.isEnabled) View.VISIBLE else View.GONE

            // 保存样式到配置
            styleConfig.saveStyle(style)
            Log.d(TAG, "更新字幕样式")
        }
    }

    /**
     * 获取当前字幕样式
     */
    fun getCurrentSubtitleStyle(): SubtitleStyleConfig.SubtitleStyle {
        return styleConfig.loadStyle()
    }

    /**
     * 获取样式配置管理器
     */
    fun getStyleConfig(): SubtitleStyleConfig {
        return styleConfig
    }

    /**
     * 显示/隐藏字幕
     */
    fun setSubtitleVisible(visible: Boolean) {
        if (subtitleView != null) {
            subtitleView?.visibility = if (visible) View.VISIBLE else View.GONE
            Log.d(TAG, "字幕可见性: $visible")
        }
    }

    /********** 以下重载 GSYVideoPlayer 的全屏 SubtitleView 相关实现 **********/

    override fun startWindowFullscreen(
        context: Context,
        actionBar: Boolean,
        statusBar: Boolean
    ): GSYBaseVideoPlayer {
        val gsyBaseVideoPlayer = super.startWindowFullscreen(context, actionBar, statusBar)
        val subtitleVideoPlayer = gsyBaseVideoPlayer as SubtitleVideoPlayer
        
        // 在全屏模式下添加字幕输出监听器
        getSubtitleVideoManager().addTextOutputPlaying(subtitleVideoPlayer)
        
        Log.d(TAG, "进入全屏模式")
        return gsyBaseVideoPlayer
    }

    override fun resolveNormalVideoShow(oldF: View?, vp: ViewGroup?, gsyVideoPlayer: GSYVideoPlayer?) {
        super.resolveNormalVideoShow(oldF, vp, gsyVideoPlayer)
        val subtitleVideoPlayer = gsyVideoPlayer as? SubtitleVideoPlayer
        
        if (subtitleVideoPlayer != null) {
            // 退出全屏时移除字幕输出监听器
            getSubtitleVideoManager().removeTextOutput(subtitleVideoPlayer)
        }
        
        Log.d(TAG, "退出全屏模式")
    }

    /********** 以下重载GSYVideoPlayer的GSYVideoViewBridge相关实现 **********/

    /**
     * 获取字幕视频管理器
     */
    private fun getSubtitleVideoManager(): SubtitleVideoManager {
        SubtitleVideoManager.instance().initContext(context.applicationContext)
        return SubtitleVideoManager.instance()
    }

    override fun getGSYVideoManager(): SubtitleVideoManager {
        return getSubtitleVideoManager()
    }

    override fun backFromFull(context: Context): Boolean {
        return SubtitleVideoManager.backFromWindowFull(context)
    }

    override fun releaseVideos() {
        SubtitleVideoManager.releaseAllVideos()
    }

    override fun getFullId(): Int {
        return SubtitleVideoManager.FULLSCREEN_ID
    }

    override fun getSmallId(): Int {
        return SubtitleVideoManager.SMALL_ID
    }
}
