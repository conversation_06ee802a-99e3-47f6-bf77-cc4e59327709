package com.tvplayer.webdav.ui.player.subtitle

import androidx.media3.common.Player

/**
 * 字幕播放器模型
 * 包含播放器所需的所有配置信息
 */
data class SubtitleModel(
    val url: String,
    val subtitlePath: String? = null,
    val isLooping: Boolean = false,
    val mapHeadData: Map<String, String>? = null,
    val isCache: Boolean = false,
    val cachePath: String? = null,
    val overrideExtension: String? = null,
    val speed: Float = 1f,
    val textOutput: Player.Listener? = null
)
