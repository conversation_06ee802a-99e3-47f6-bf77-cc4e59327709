<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <!-- 标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="字幕功能测试"
            android:textColor="@android:color/white"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="32dp" />

        <!-- 字幕下载测试 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="字幕下载测试"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_test_download"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="测试字幕下载"
            android:textColor="@android:color/white"
            android:backgroundTint="@color/design_default_color_primary"
            android:layout_marginBottom="16dp" />

        <!-- 视频播放测试 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="视频播放测试"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_test_playback"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="播放带字幕视频"
            android:textColor="@android:color/white"
            android:backgroundTint="@color/design_default_color_secondary"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_test_playback_no_subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="播放无字幕视频"
            android:textColor="@android:color/white"
            android:backgroundTint="@color/design_default_color_secondary"
            android:layout_marginBottom="16dp" />

        <!-- 缓存管理 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="缓存管理"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_check_cache"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="检查缓存状态"
            android:textColor="@android:color/white"
            android:backgroundTint="@android:color/holo_blue_dark"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_clear_cache"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="清理缓存"
            android:textColor="@android:color/white"
            android:backgroundTint="@android:color/holo_red_dark"
            android:layout_marginBottom="16dp" />

        <!-- 说明文字 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="测试说明：\n\n1. 点击「测试字幕下载」验证字幕文件下载功能\n2. 点击「播放带字幕视频」测试字幕显示效果\n3. 点击「播放无字幕视频」对比无字幕播放\n4. 使用缓存管理功能检查和清理字幕缓存\n\n注意：首次下载字幕可能需要一些时间，请耐心等待。"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:lineSpacingExtra="4dp"
            android:layout_marginTop="24dp"
            android:padding="16dp"
            android:background="@drawable/rounded_background"
            android:alpha="0.8" />

    </LinearLayout>

</ScrollView>
