<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <!-- 视频播放表面 -->
    <com.shuyu.gsyvideoplayer.video.base.GSYTextureView
        android:id="@+id/surface_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" />

    <!-- 字幕显示组件 -->
    <androidx.media3.ui.SubtitleView
        android:id="@+id/subtitle_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="80dp"
        app:subtitle_bottom_padding="16dp" />

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/loading"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_centerInParent="true"
        android:indeterminateDrawable="@drawable/video_loading_drawable"
        android:visibility="invisible" />

    <!-- 播放按钮 -->
    <ImageView
        android:id="@+id/start"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerInParent="true"
        android:background="@drawable/click_back"
        android:clickable="true"
        android:focusable="true"
        android:scaleType="centerInside"
        android:src="@drawable/video_click_play_selector"
        android:visibility="invisible" />

    <!-- 控制栏容器 -->
    <RelativeLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/video_bottom_shadow"
        android:gravity="center_vertical"
        android:visibility="invisible">

        <!-- 当前时间 -->
        <TextView
            android:id="@+id/current"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:layout_marginStart="16dp"
            android:gravity="center"
            android:text="00:00"
            android:textColor="@android:color/white"
            android:textSize="12sp" />

        <!-- 总时间 -->
        <TextView
            android:id="@+id/total"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:gravity="center"
            android:text="00:00"
            android:textColor="@android:color/white"
            android:textSize="12sp" />

        <!-- 进度条 -->
        <SeekBar
            android:id="@+id/progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:layout_toEndOf="@id/current"
            android:layout_toStartOf="@id/total"
            android:max="100"
            android:maxHeight="4dp"
            android:minHeight="4dp"
            android:progressDrawable="@drawable/video_seek_progress"
            android:splitTrack="false"
            android:thumb="@drawable/video_seek_thumb" />

    </RelativeLayout>

    <!-- 顶部控制栏 -->
    <RelativeLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_alignParentTop="true"
        android:background="@drawable/video_title_shadow"
        android:gravity="center_vertical"
        android:visibility="invisible">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="@drawable/click_back"
            android:clickable="true"
            android:focusable="true"
            android:padding="12dp"
            android:scaleType="centerInside"
            android:src="@drawable/video_back" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:layout_toEndOf="@id/back"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textColor="@android:color/white"
            android:textSize="16sp" />

    </RelativeLayout>

    <!-- 全屏按钮 -->
    <ImageView
        android:id="@+id/fullscreen"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/click_back"
        android:clickable="true"
        android:focusable="true"
        android:padding="8dp"
        android:scaleType="centerInside"
        android:src="@drawable/video_enlarge"
        android:visibility="invisible" />

    <!-- 缓冲进度条 -->
    <ProgressBar
        android:id="@+id/bottom_seek_progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="1.5dp"
        android:layout_alignParentBottom="true"
        android:max="100"
        android:progressDrawable="@drawable/video_seek_progress"
        android:visibility="invisible" />

</RelativeLayout>
