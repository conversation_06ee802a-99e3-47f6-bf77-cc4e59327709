<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <vector android:width="80dp"
            android:height="80dp"
            android:viewportWidth="24"
            android:viewportHeight="24">
            <path
                android:fillColor="#CCFFFFFF"
                android:pathData="M8,5v14l11,-7z"/>
        </vector>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <vector android:width="80dp"
            android:height="80dp"
            android:viewportWidth="24"
            android:viewportHeight="24">
            <path
                android:fillColor="@android:color/white"
                android:pathData="M8,5v14l11,-7z"/>
        </vector>
    </item>
    
</selector>
