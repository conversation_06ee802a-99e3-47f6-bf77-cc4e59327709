<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 背景 -->
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <solid android:color="#33FFFFFF" />
            <corners android:radius="2dp" />
        </shape>
    </item>
    
    <!-- 缓冲进度 -->
    <item android:id="@android:id/secondaryProgress">
        <clip>
            <shape android:shape="rectangle">
                <solid android:color="#66FFFFFF" />
                <corners android:radius="2dp" />
            </shape>
        </clip>
    </item>
    
    <!-- 播放进度 -->
    <item android:id="@android:id/progress">
        <clip>
            <shape android:shape="rectangle">
                <solid android:color="@color/accent_color" />
                <corners android:radius="2dp" />
            </shape>
        </clip>
    </item>
    
</layer-list>
