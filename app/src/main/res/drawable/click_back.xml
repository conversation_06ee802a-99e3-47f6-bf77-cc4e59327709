<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#66FFFFFF" />
        </shape>
    </item>
    
    <!-- 聚焦状态 -->
    <item android:state_focused="true">
        <shape android:shape="oval">
            <solid android:color="#44FFFFFF" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#22FFFFFF" />
        </shape>
    </item>
    
</selector>
