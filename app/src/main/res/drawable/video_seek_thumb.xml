<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="@color/accent_color" />
            <size android:width="16dp" android:height="16dp" />
        </shape>
    </item>
    
    <!-- 聚焦状态 -->
    <item android:state_focused="true">
        <shape android:shape="oval">
            <solid android:color="@color/accent_color" />
            <size android:width="14dp" android:height="14dp" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@android:color/white" />
            <size android:width="12dp" android:height="12dp" />
        </shape>
    </item>
    
</selector>
