# 字幕功能使用指南

## 🎯 快速开始

### 1. 测试字幕功能

1. **启动应用**
   - 运行 aj-tv-player 应用
   - 进入主界面

2. **进入测试页面**
   - 在主界面的分类中找到"字幕测试"选项
   - 点击进入字幕测试页面

3. **测试字幕下载**
   - 点击"测试字幕下载"按钮
   - 等待字幕文件下载完成
   - 查看下载结果提示

4. **测试视频播放**
   - 点击"播放带字幕视频"测试字幕显示效果
   - 点击"播放无字幕视频"对比播放效果

### 2. 在代码中使用字幕功能

#### 基本用法

```kotlin
// 创建带字幕的播放Intent
val intent = PlayerActivity.intentForWithSubtitle(
    context,
    "少林足球",  // 视频标题
    Uri.parse("视频URL"),  // 视频地址
    "http://file1.assrt.net/onthefly/665530/-/1/Shaolin.Soccer.2001.CHINESE.1080p.BluRay.x264.DTS-HD.MA.5.1-FGT.ass?_=1755913614&-=39d6f7172f3afa9a93cd2f7d3bce95b2&api=1"  // 字幕URL
)
startActivity(intent)
```

#### 高级用法

```kotlin
// 手动下载字幕
val subtitleDownloader = SubtitleDownloader(context)
lifecycleScope.launch {
    when (val result = subtitleDownloader.downloadSubtitle(subtitleUrl)) {
        is SubtitleDownloader.DownloadResult.Success -> {
            // 字幕下载成功，获取本地路径
            val localPath = result.filePath
            // 设置到播放器
            videoPlayer.setSubtitlePath(localPath)
        }
        is SubtitleDownloader.DownloadResult.Error -> {
            // 处理下载错误
            Log.e("Subtitle", "下载失败: ${result.message}")
        }
    }
}
```

## ⚙️ 字幕样式配置

### 使用预设样式

```kotlin
val styleConfig = SubtitleStyleConfig(context)

// 获取预设样式
val presetStyles = styleConfig.getPresetStyles()
// 选择一个预设样式，例如"黄色字幕"
val yellowStyle = presetStyles.find { it.first == "黄色字幕" }?.second

// 应用样式
if (yellowStyle != null) {
    videoPlayer.setSubtitleStyle(yellowStyle)
}
```

### 自定义样式

```kotlin
// 创建自定义样式
val customStyle = SubtitleStyleConfig.SubtitleStyle(
    textColor = Color.CYAN,           // 青色文字
    backgroundColor = Color.TRANSPARENT,  // 透明背景
    edgeType = CaptionStyleCompat.EDGE_TYPE_OUTLINE,  // 轮廓边缘
    edgeColor = Color.BLACK,          // 黑色边缘
    textSize = 18f,                   // 18sp字体大小
    isEnabled = true                  // 启用字幕
)

// 应用自定义样式
videoPlayer.setSubtitleStyle(customStyle)
```

### 样式选项说明

#### 文字颜色选项
- 白色 (Color.WHITE)
- 黄色 (Color.YELLOW)  
- 绿色 (Color.GREEN)
- 蓝色 (Color.CYAN)
- 红色 (Color.RED)
- 紫色 (Color.MAGENTA)
- 橙色 (Color.rgb(255, 165, 0))

#### 边缘效果选项
- 无边缘 (CaptionStyleCompat.EDGE_TYPE_NONE)
- 轮廓 (CaptionStyleCompat.EDGE_TYPE_OUTLINE)
- 投影 (CaptionStyleCompat.EDGE_TYPE_DROP_SHADOW)
- 凸起 (CaptionStyleCompat.EDGE_TYPE_RAISED)
- 凹陷 (CaptionStyleCompat.EDGE_TYPE_DEPRESSED)

#### 字体大小选项
- 很小: 10sp
- 小: 12sp
- 正常: 16sp
- 大: 20sp
- 很大: 24sp
- 超大: 28sp

## 🗂️ 缓存管理

### 检查缓存状态

```kotlin
val subtitleDownloader = SubtitleDownloader(context)

// 检查特定字幕是否已缓存
val isCached = subtitleDownloader.isSubtitleCached(subtitleUrl)

// 获取缓存文件路径
val cachedPath = subtitleDownloader.getCachedSubtitlePath(subtitleUrl)

// 获取缓存总大小
lifecycleScope.launch {
    val cacheSize = subtitleDownloader.getCacheSize()
    Log.d("Cache", "缓存大小: ${cacheSize / 1024}KB")
}
```

### 清理缓存

```kotlin
lifecycleScope.launch {
    // 清理7天前的缓存文件
    subtitleDownloader.clearCache(maxAgeMillis = 7 * 24 * 60 * 60 * 1000L)
    
    // 清理所有缓存文件
    subtitleDownloader.clearCache(maxAgeMillis = 0L)
}
```

## 🔧 故障排除

### 常见问题

1. **字幕不显示**
   - 检查字幕URL是否可访问
   - 确认字幕文件格式是否支持 (.ass, .srt, .vtt)
   - 检查字幕是否启用：`styleConfig.isSubtitleEnabled()`

2. **字幕下载失败**
   - 检查网络连接
   - 确认应用有网络权限
   - 查看错误日志获取详细信息

3. **字幕显示位置不正确**
   - 调整字幕样式设置
   - 检查视频播放器布局是否正确

4. **字幕与视频不同步**
   - 确认字幕文件与视频匹配
   - 检查字幕文件时间轴是否正确

### 调试技巧

1. **启用详细日志**
   ```kotlin
   // 在代码中添加日志输出
   Log.d("Subtitle", "字幕下载状态: $result")
   Log.d("Subtitle", "字幕路径: $subtitlePath")
   ```

2. **检查文件完整性**
   ```kotlin
   // 检查下载的字幕文件是否完整
   val file = File(subtitlePath)
   Log.d("Subtitle", "文件大小: ${file.length()} bytes")
   Log.d("Subtitle", "文件存在: ${file.exists()}")
   ```

3. **测试不同格式**
   - 尝试使用不同格式的字幕文件
   - 测试简单的SRT格式字幕

## 📱 支持的字幕格式

### ASS/SSA 格式
- **特点**: 高级字幕格式，支持丰富样式
- **扩展名**: .ass, .ssa
- **支持**: ✅ 完全支持

### SRT 格式  
- **特点**: 简单文本字幕，广泛兼容
- **扩展名**: .srt
- **支持**: ✅ 完全支持

### VTT 格式
- **特点**: Web字幕格式
- **扩展名**: .vtt
- **支持**: ✅ 基本支持

## 🎨 最佳实践

1. **字幕文件选择**
   - 优先选择与视频匹配的字幕文件
   - 确保字幕编码为UTF-8
   - 选择时间轴准确的字幕

2. **样式设置**
   - 根据视频内容选择合适的字幕颜色
   - 在暗色场景使用白色或黄色字幕
   - 在亮色场景使用深色字幕或添加背景

3. **性能优化**
   - 利用缓存机制避免重复下载
   - 定期清理过期缓存文件
   - 选择合适的字体大小

4. **用户体验**
   - 提供字幕开关选项
   - 允许用户自定义字幕样式
   - 在字幕加载时显示进度提示

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 查看应用日志获取错误信息
2. 确认网络连接和权限设置
3. 尝试使用测试功能验证基本功能
4. 检查字幕文件URL和格式是否正确

字幕功能已完全集成到播放器中，享受更好的观影体验！🎬
